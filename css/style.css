@import url('https://fonts.googleapis.com/css2?family=Barlow+Condensed:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Oswald:wght@200..700&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');

/* === Icon with Text (Standard) === */
.nw-icon-with-text {
  display: flex;
  gap: 1.5rem;
  padding-bottom: 2rem;
  align-items: flex-start;
}

.nw-icon-with-text.text-center {
  justify-content: center;
}

.nw-icon-with-text span.vc_icon_element-icon:before {
  background: #367a35;
  color: #fff;
  font-size: 1.5rem;
  padding: 0.5rem;
  border-radius: 999px;
  display: flex;
  width: 32px;
  height: 32px;
  justify-content: center;
  align-items: center;
}

.nw-icon-with-text h3 {
  margin: 0;
  line-height: 1.4;
}

.nw-icon-with-text p {
  font-size: 1rem;
  line-height: 1.8;
  margin-top: 0.5rem;
  text-wrap: pretty;
}


/* === Custom Icon with Text === */
.nw-custom-icon-with-text {
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
  text-wrap: balance;
}

.nw-custom-icon-with-text.text-center {
  align-items: center;
  text-align: center;
}

.nw-custom-icon-with-text .custom-icon-wrapper {
  display: flex;
  align-items: center;
  aspect-ratio: 1 / 1;
  margin-bottom: 1rem;
}

.nw-custom-icon-with-text h3 {
  margin: 0;
}

.nw-custom-icon-with-text .content {
  width: 100%;
}

.nw-custom-icon-with-text p {
  font-weight: 300;
  margin-top: 0;
}

.custom-icon-wrapper svg path {
  fill: #008dea !important;
}


/* === Hero Section === */
.hero-fullwidth-section {
  position: relative;
  padding: 80px 0;
}

.hero-fullwidth-section .hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: center / cover no-repeat;
  z-index: 0;
  opacity: 0.6;
}


.hero-fullwidth-section .main-content {
  position: relative;
  z-index: 1;
  max-width: 1425px;
  padding: 0 90px;
  margin: 0 auto;
  color: #fff;
}

.hero-fullwidth-section .hero-container {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  flex-wrap: wrap;
}

.hero-fullwidth-section .hero-text {
  flex: 1 1 50%;
  max-width: 600px;
  padding-right: 30px;
}

.hero-fullwidth-section .hero-buttons {
  margin-top: 20px;
}

.hero-fullwidth-section .btn {
  display: inline-block;
  padding: 12px 24px;
  margin-right: 10px;
  background-color: #0073aa;
  color: #fff;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.hero-fullwidth-section .btn.secondary {
  background-color: #444;
}

.hero-fullwidth-section .hero-image {
  flex: 1 1 40%;
  text-align: center;
}

.hero-fullwidth-section .hero-image img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
  aspect-ratio: 16 / 9;
}

#three-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none; /* Wichtiger Schutz für Klicks auf Buttons über dem Canvas */
}

#three-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* === Additional Hero Content === */
.hero-section {
  max-width: 1500px;
  margin: 0 auto;
  padding: 0;
}

.hero-content {
  margin: 0 auto;
}

.hero-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
  margin-bottom: 3rem;
}

.hero-text h1 {
  margin: 0 0 1.5rem;
  font-size: 2.5rem;
  line-height: 1.2;
  color: #333;
  text-wrap: balance;
}

.hero-text p {
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: #666;
  text-wrap: balance;
  text-align: left;
}

.hero-text .cta-button {
  display: inline-block;
  padding: 0.4rem 1.2rem;
  background-color: #367a35;
  border: 2px solid #2b5c2a;
  color: #fff;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.hero-text .cta-button:hover {
  background-color: #367a33;
  border-color: #2b5c2a;
}

.hero-text .cta-button.secondary {
  background-color: transparent;
  color: #367a33;
  border-color: #2b5c2a;
}

.hero-text .cta-button.secondary:hover {
  color: #367a33;
  border-color: #2b5c2a;
}

.hero-section .google-reviews-section {
  display: flex;
  gap: 0.5rem;
  background: #fff;
  padding: 0.5rem;
  border-radius: 999px;
  width: fit-content;
  margin-bottom: 2rem;
}


/* === Partner Section === */
.partner-section {
  text-align: center;
  margin-top: 3rem;
}

.partner-text {
  margin-bottom: 2rem;
  color: #666;
  text-wrap: balance;
}

.partner-logos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.partner-logo {
  max-width: 150px;
}

.partner-logo img {
  width: 100%;
  height: auto;
  transition: opacity 0.3s ease;
}


/* === Responsive Anpassungen === */
@media (max-width: 999px) {
  .hero-content {
    padding: 3rem 0 2rem;
  }

  .hero-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .hero-image {
    width: 100%;
    margin-left: 0;
  }

  .hero-text {
    padding-right: 0;
    text-align: center;
  }

  .hero-text h1 {
    font-size: 1.6rem;
  }

  .hero-text .cta-button {
    margin-bottom: 0.5rem;
  }

  .hero-text .cta-button:last-child {
    margin-bottom: 0 !important;
  }

  .partner-logos {
    gap: 2rem;
  }

  .partner-logo {
    max-width: 120px;
  }

  .hero-fullwidth-section .hero-container {
    flex-direction: column;
    text-align: center;
  }

  .hero-fullwidth-section .hero-text {
    padding-right: 0;
    margin-bottom: 2rem;
    text-align: left;
  }

  .hero-fullwidth-section .hero-image {
    width: 100%;
  }
}



/***
 * Hero Fullwidth Background Section
 */

section.hero-fullwidth-section {
  background: radial-gradient(#4e4334, #281d0f);
  background: radial-gradient(#665844, #281d0f);
  background: radial-gradient(#7e6047, #24150e);
}

/** centered content */
.centered-content {
  position: absolute !important;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%; 
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}


/* positionierung */
.centered-content.hero-background-content {
  z-index: 0;
}

.centered-content.hero-foreground-content {
  z-index: 9;
}

.three-canvas-container {
  z-index: 1;
}


/* Headline styles */ 
.centered-content p.subheadline {
  background: rgb(255 255 255 / 10%);
  border-radius: 0 !important;
  margin-bottom: 1rem;
  font-weight: 300;
  backdrop-filter: blur(12px);
  font-size: 24px;
  padding: 0.8rem 1.5rem !important;
  border-radius: 999px !important;
  margin-top: 3rem;
}

.centered-content h1, p.headline {
  max-width: 800px;
  font-size: 5rem;
  line-height: 1;
  text-transform: uppercase;
  color: #fff;
  text-wrap: balance;
  margin: 0;
  margin: 0 !important;
  padding: 0 !important;
  font-weight: 900;
}

p.headline {
  text-shadow: 0 0 2rem rgb(0 0 0 / 30%), 0 0 5rem rgb(0 0 0 / 30%), 0 0 3rem rgb(0 0 0 / 30%);
}

.centered-content h1 {
  color: transparent; /* Füllung transparent */
  -webkit-text-stroke: 3px black; /* Outline-Farbe und -Dicke */
}
