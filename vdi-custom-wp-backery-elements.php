<?php
/*
Plugin Name: VDI WP-Bakery Elements
Plugin URI: https://newwways.de
Description: <PERSON><PERSON><PERSON><PERSON>t WPBakery Page Builder um benutzerdefinierte Elemente.
Version: 1.0
Author: <PERSON>
Author URI: https://newwways.de
*/


// Verhindere direkten Zugriff
if (!defined('ABSPATH')) {
    exit;
}

define('KISHAS_PLUGIN_URL', plugin_dir_url(__FILE__));

/* Custom CSS */ 
if (is_admin()) {
    add_action('admin_enqueue_scripts', 'custom_wpbakery_elements_enqueue_styles');
} else {
    add_action('wp_enqueue_scripts', 'custom_wpbakery_elements_enqueue_styles');
}

function custom_wpbakery_elements_enqueue_styles() {
    $file = plugin_dir_path(__FILE__) . 'css/style.css';
    $version = filemtime($file); // Nutzt die letzte Änderungszeit der Datei als Version
    wp_enqueue_style('custom-wpbakery-elements-style', plugins_url('css/style.css', __FILE__), array(), $version, 'all');
}

/* Load all WP-Bakery Elements */
add_action('init', function () {
    if (!function_exists('vc_map')) {
        // vc_custom_debug('❌ vc_map nicht verfügbar – WPBakery noch nicht aktiv?');
        return;
    }

    // vc_custom_debug('✅ vc_map ist verfügbar – WPBakery aktiv');
    $vc_elements_dir = plugin_dir_path(__FILE__) . 'wp-bakery-elements/';
    $files = glob($vc_elements_dir . '*.php');

    if (empty($files)) {
        // vc_custom_debug('⚠️ Keine VC-Elemente im Ordner gefunden');
        return;
    }

    foreach ($files as $file) {
        // vc_custom_debug('📂 Lade VC-Element: ' . basename($file));
        require_once $file;
    }
	

function fbw_enqueue_background_styles() {
    wp_register_style('fbw-style', false);
    wp_enqueue_style('fbw-style');

    $custom_css = "
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-image: url('https://example.com/dein-hintergrund.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            z-index: -1;
            opacity: 0.6;
        }
    ";

    wp_add_inline_style('fbw-style', $custom_css);
}
add_action('wp_enqueue_scripts', 'fbw_enqueue_background_styles');

});

