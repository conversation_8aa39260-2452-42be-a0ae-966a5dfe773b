<?php

// ===============================
// WPBakery Element Mapping
// ===============================
add_action('vc_before_init', 'register_hero_fullwidth_background_section');
function register_hero_fullwidth_background_section() {
    vc_map(array(
        'name'        => __('Hero Fullwidth Section', 'text-domain'),
        'base'        => 'hero_fullwidth_background_section',
        'description' => __('Eine Hero-Sektion mit Hintergrundbild und Buttons', 'text-domain'),
        'category'    => __('Custom Elements', 'text-domain'),
       'icon' => 'icon-wpb-images-stack',
        'params'      => array(
            array(
                'type'        => 'attach_image',
                'heading'     => __('Hintergrundbild', 'text-domain'),
                'param_name'  => 'background_image',
                'description' => __('<PERSON><PERSON><PERSON>e ein Hintergrundbild aus', 'text-domain'),
            ),
            array(
                'type'        => 'textfield',
                'heading'     => __('Headline', 'text-domain'),
                'param_name'  => 'headline',
            ),
            array(
                'type'        => 'textfield',
                'heading'     => __('Subheadline', 'text-domain'),
                'param_name'  => 'subheadline',
            ),
            array(
                'type'        => 'vc_link',
                'heading'     => __('Button 1', 'text-domain'),
                'param_name'  => 'button_1',
            ),
            array(
                'type'        => 'vc_link',
                'heading'     => __('Button 2', 'text-domain'),
                'param_name'  => 'button_2',
            ),
            array(
                'type'        => 'colorpicker',
                'heading'     => __('Headline Farbe', 'text-domain'),
                'param_name'  => 'headline_color',
            ),
            array(
                'type'        => 'textfield',
                'heading'     => __('Headline Schriftgröße (z.B. 48px)', 'text-domain'),
                'param_name'  => 'headline_font_size',
            ),
            array(
                'type'        => 'colorpicker',
                'heading'     => __('Subheadline Farbe', 'text-domain'),
                'param_name'  => 'subheadline_color',
            ),
            array(
                'type'        => 'textfield',
                'heading'     => __('Subheadline Schriftgröße (z.B. 24px)', 'text-domain'),
                'param_name'  => 'subheadline_font_size',
            ),
            array(
                'type'        => 'textfield',
                'heading'     => __('Hintergrund Border (z.B. 1px solid #000)', 'text-domain'),
                'param_name'  => 'background_border',
            ),
            array(
                'type'        => 'textfield',
                'heading'     => __('Hintergrund Border Radius (z.B. 12px)', 'text-domain'),
                'param_name'  => 'background_border_radius',
            ),
        ),
    ));
}

// ===============================
// Hero Fullwidth Background Section
// ===============================

// Shortcode registrieren
add_shortcode('hero_fullwidth_background_section', 'render_hero_fullwidth_background_section');

function render_hero_fullwidth_background_section($atts) {
    $atts = shortcode_atts(array(
        'background_image'                => '',
        'headline'                        => '',
        'subheadline'                     => '',
        'button_1'                        => '',
        'button_2'                        => '',
        'headline_color'                  => '',
        'headline_font_size'              => '',
        'subheadline_color'               => '',
        'subheadline_font_size'           => '',
        'background_border'               => '',
        'background_border_radius'        => '',
        'headline_background_color'       => '',
        'headline_background_opacity'     => '',
        'headline_border_radius'          => '',
        'subheadline_background_color'    => '',
        'subheadline_background_opacity'  => '',
        'subheadline_border_radius'       => '',
    ), $atts);

    // 🖼 Hintergrundbild laden
    if (!empty($atts['background_image']) && function_exists('wp_get_attachment_image_url')) {
        $bg_url = wp_get_attachment_image_url($atts['background_image'], 'full');
    } elseif (!empty($atts['background_image'])) {
        $bg_url = wp_get_attachment_url($atts['background_image']); // Fallback
    } else {
        $bg_url = '';
    }

    // 🔗 Buttons (nur wenn WPBakery aktiv)
    if (function_exists('vc_build_link')) {
        $btn1 = vc_build_link($atts['button_1']);
        $btn2 = vc_build_link($atts['button_2']);
    } else {
        $btn1 = $btn2 = ['url' => '', 'title' => ''];
    }

    // 🎨 Section-Styles
    $section_style = '';
    if (!empty($atts['background_border_radius'])) {
        $section_style .= "border-radius:" . esc_attr($atts['background_border_radius']) . "; overflow: hidden; position: relative; height: 100vh;";
    }
    if(!empty($attrs['background_color'])) {
        $section_style .= "background-color:" . esc_attr($atts['background_color']) . ";";
    } else {
        $section_style .= "background-color: #281d0f;";
    }
    if (!empty($atts['background_border'])) {
        $section_style .= " border:" . esc_attr($atts['background_border']) . ";";
    }
    if (!empty($bg_url)) {
        $section_style .= " background-image: url('" . esc_url($bg_url) . "'); background-size: cover; background-position: center;";
    }

    // 🎨 Headline Styles
    $headline_style = '';
    if (!empty($atts['headline_color'])) {
        $headline_style .= 'color:' . esc_attr($atts['headline_color']) . ';';
    }

    $foreground_headline_style = '';
    if (!empty($atts['headline_font_size'])) {
        $foreground_headline_style .= '-webkit-text-stroke: 3px' . esc_attr($atts['headline_color']) . ';';
    }

    // 🎨 Subheadline Styles
    $subheadline_style = '';
    if (!empty($atts['subheadline_color'])) {
        $subheadline_style .= 'color:' . esc_attr($atts['subheadline_color']) . ';';
    }
    if (!empty($atts['subheadline_font_size'])) {
        $subheadline_style .= 'font-size:' . esc_attr($atts['subheadline_font_size']) . ';';
    }
    if (!empty($atts['subheadline_background_color'])) {
        $opacity = (!empty($atts['subheadline_background_opacity'])) ? floatval($atts['subheadline_background_opacity']) : 1;
        $subheadline_style .= 'background-color:' . kishas_hex_to_rgba($atts['subheadline_background_color'], $opacity) . ';';
    }
    if (!empty($atts['subheadline_border_radius'])) {
        $subheadline_style .= 'border-radius:' . esc_attr($atts['subheadline_border_radius']) . ';';
    }
    $subheadline_style .= 'padding: 8px 16px; display:inline-block;';

    ob_start();
    ?>
    <section class="hero-fullwidth-section" style="<?php echo esc_attr($section_style); ?>">

        <!-- Content behind -->
        <div class="container centered-content hero-background-content" style="max-width: 1425px; padding: 0 90px; margin: 0 auto;">
            <?php if (!empty($atts['subheadline'])): ?>
                <p class="subheadline" style="<?php echo esc_attr($subheadline_style . ' background-color: #ab7c4b !important; background-color: #916e49 !important;'); ?>">
                    <?php echo esc_html($atts['subheadline']); ?>
                </p>
            <?php endif; ?>

            <?php if (!empty($atts['headline'])): ?>
                <p class="headline" style="<?php echo esc_attr($headline_style); ?>">
                    <?php echo esc_html($atts['headline']); ?>
                </p>
            <?php endif; ?>

            <div class="hero-buttons">
                <?php if (!empty($btn1['url'])): ?>
                    <a href="<?php echo esc_url($btn1['url']); ?>" class="btn">
                        <?php echo esc_html($btn1['title']); ?>
                    </a>
                <?php endif; ?>
                <?php if (!empty($btn2['url'])): ?>
                    <a href="<?php echo esc_url($btn2['url']); ?>" class="btn secondary">
                        <?php echo esc_html($btn2['title']); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <div id="loading">
            <div class="spinner"></div>
            Lade 3D Modell...
        </div>

        <!-- Canvas Container -->
        <div class="three-canvas-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; justify-content: center; display: flex; align-items: center;">
            <canvas id="three-canvas"></canvas>
        </div>

        <!-- Content oben drauf -->
        <div class="container centered-content hero-foreground-content" style="max-width: 1425px; padding: 0 90px; margin: 0 auto;">
            <?php if (!empty($atts['subheadline'])): ?>
                <p class="subheadline" style="<?php echo esc_attr($subheadline_style); ?>">
                    <?php echo esc_html($atts['subheadline']); ?>
                </p>
            <?php endif; ?>

            <?php if (!empty($atts['headline'])): ?>
                <h1 style="<?php echo esc_attr($foreground_headline_style); ?>">
                    <?php echo esc_html($atts['headline']); ?>
                </h1>
            <?php endif; ?>

            <div class="hero-buttons">
                <?php if (!empty($btn1['url'])): ?>
                    <a href="<?php echo esc_url($btn1['url']); ?>" class="btn">
                        <?php echo esc_html($btn1['title']); ?>
                    </a>
                <?php endif; ?>
                <?php if (!empty($btn2['url'])): ?>
                    <a href="<?php echo esc_url($btn2['url']); ?>" class="btn secondary">
                        <?php echo esc_html($btn2['title']); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>

        
    </section>

    <!--
    <script type="module">
        import * as THREE from '<?php echo KISHAS_PLUGIN_URL; ?>js/three.module.js';

        document.addEventListener('DOMContentLoaded', function () {
            const canvas = document.getElementById('three-canvas');
            if (!canvas) return;

            // Szene & Kamera
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 5;

            // Renderer
            const renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);

            // Einfacher Würfel
            const geometry = new THREE.BoxGeometry();
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
            const cube = new THREE.Mesh(geometry, material);
            scene.add(cube);

            function animate() {
                requestAnimationFrame(animate);
                cube.rotation.x += 0.01;
                cube.rotation.y += 0.01;
                renderer.render(scene, camera);
            }

            animate();
        });
    </script>
    -->

    <!-- Three.js + GLTFLoader (Legacy) -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/three@0.149.0/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.149.0/examples/js/loaders/GLTFLoader.min.js"></script> -->


    <!-- Polyfill für Import Maps in allen Browsern -->
    <script async src="https://ga.jspm.io/npm:es-module-shims@1.5.1/dist/es-module-shims.js"></script>
    <!-- Die Import Map, die "three" usw. auf ein CDN mapped -->
    <script type="importmap">
    {
    "imports": {
        "three": "https://cdn.jsdelivr.net/npm/three@0.165.0/build/three.module.js",
        "three/addons/": "https://cdn.jsdelivr.net/npm/three@0.165.0/examples/jsm/"
    }
    }
    </script>
    <!-- 
    <script type="module">
        import * as THREE from 'https://cdn.jsdelivr.net/npm/three@0.165.0/build/three.module.js';
        // import { GLTFLoader } from 'https://cdn.jsdelivr.net/npm/three@0.165.0/examples/jsm/loaders/GLTFLoader.js';
        import { GLTFLoader } from "three/addons/loaders/GLTFLoader.js";
        
        document.addEventListener('DOMContentLoaded', function () {
            const canvas = document.getElementById('three-canvas');
            if (!canvas) return;

            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 1, 3);

            const renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.setClearColor(0x000000, 0);

            const light = new THREE.DirectionalLight(0xffffff, 1);
            light.position.set(2, 2, 5);
            scene.add(light);
            scene.add(new THREE.AmbientLight(0xffffff, 0.5));

            const loader = new GLTFLoader();
            // loader.load(
            //     'https://kishasplayground6297.live-website.com/wp-content/uploads/2025/07/nepalese_tribal_mask.glb',
            //     function (gltf) {
            //         const model = gltf.scene;
            //         scene.add(model);

            //         function animate() {
            //             requestAnimationFrame(animate);
            //             model.rotation.y += 0.005;
            //             renderer.render(scene, camera);
            //         }
            //         animate();
            //     },
            //     undefined,
            //     function (error) {
            //         console.error('❌ Fehler beim Laden des Modells:', error);
            //     }
            // );

            // create a simple cube 
            const geometry = new THREE.BoxGeometry();
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
            const cube = new THREE.Mesh(geometry, material);
            scene.add(cube);

            // Animation Loop - DAS WAR DAS FEHLENDE TEIL!
            function animate() {
                requestAnimationFrame(animate);
                
                // Rotate the cube
                cube.rotation.x += 0.01;
                cube.rotation.y += 0.01;
                
                // Render the scene
                renderer.render(scene, camera);
            }
            
            // Start the animation
            animate();

            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        });
    </script>
    -->

    <script type="module">
        import * as THREE from 'three';
        import { GLTFLoader } from "three/addons/loaders/GLTFLoader.js";
        import GUI from 'https://cdn.jsdelivr.net/npm/lil-gui@0.19/+esm';
        
        document.addEventListener('DOMContentLoaded', function () {
            const canvas = document.getElementById('three-canvas');
            const loadingDiv = document.getElementById('loading');
            
            if (!canvas) {
                console.error('Canvas element not found!');
                return;
            }

            // Scene Setup
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 3);

            const renderer = new THREE.WebGLRenderer({ 
                canvas: canvas, 
                antialias: true, 
                alpha: true 
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.setClearColor(0x000000, 0);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // Enhanced Lighting - helleres Setup für bessere Sichtbarkeit
            const directionalLight = new THREE.DirectionalLight(0xffffff, 7);
            directionalLight.position.set(10, 5, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            const ambientLight = new THREE.AmbientLight(0x404040, 30);
            scene.add(ambientLight);
            
            // TODO: Try to spin the model but control the position of the directional Light or maybe a point Light with the mouse 

            // Zusätzliches Licht von der anderen Seite
            const fillLight = new THREE.DirectionalLight(0xffffff, 0.5);
            fillLight.position.set(5, 0, -5);
            scene.add(fillLight);

            // Fallback Cube (wird angezeigt, falls GLTF nicht lädt)
            const geometry = new THREE.BoxGeometry(1, 1, 1);
            const material = new THREE.MeshPhongMaterial({ 
                color: 0x00ff00,
                shininess: 100
            });
            const cube = new THREE.Mesh(geometry, material);
            cube.castShadow = true;
            cube.receiveShadow = true;
            scene.add(cube);

            let model = null;
            let modelGroup = new THREE.Group();

            // set model group position
            // modelGroup.position.set(0, -0.1, 3.5);
            // modelGroup.position.set(0, -0.1, 0);
            modelGroup.position.set(0, 0.14, 0.7);
            scene.add(modelGroup);

            let animationRunning = false;

            // GLTF Loader
            const loader = new GLTFLoader();
            
            console.log('🔄 Starte GLTF Loader...');
            
            loader.load(
                // 'https://kishasplayground6297.live-website.com/wp-content/uploads/2025/07/nepalese_tribal_mask.glb',
                // 'https://kishasplayground6297.live-website.com/wp-content/uploads/2025/07/vans_shoe.glb',
                // 'https://kishasplayground6297.live-website.com/wp-content/uploads/2025/07/ganesha_wooden_mask.glb',
                // 'https://kishasplayground6297.live-website.com/wp-content/uploads/2025/07/colossal_head_of_a_deva.glb',
                // 'https://kishasplayground6297.live-website.com/wp-content/uploads/2025/07/colossal_head_of_a_deva_8mb.glb',
                'https://kishasplayground6297.live-website.com/wp-content/uploads/2025/07/seated_buddha_sculpture_10mb.glb',
                function (gltf) {
                    console.log('✅ GLTF Modell erfolgreich geladen!', gltf);
                    
                    model = gltf.scene;

                    // model.traverse((child) => {
                    //     if (child.isMesh) {
                    //         console.log('🎨 Material von', child.name, ':', child.material);
                            
                    //         // Falls das Material zu dunkel ist, überschreibe es temporär:
                    //         child.material = new THREE.MeshLambertMaterial({ 
                    //             color: 0xffffff,  // Weiß für maximale Helligkeit
                    //             side: THREE.DoubleSide  // Beide Seiten beleuchten
                    //         });
                    //     }
                    // });

                    // Debug: Analysiere das Modell
                    const box = new THREE.Box3().setFromObject(model);
                    const center = box.getCenter(new THREE.Vector3());
                    const size = box.getSize(new THREE.Vector3());

                    console.log('📏 Modell Größe:', size);
                    console.log('📍 Modell Zentrum:', center);
                    console.log('📦 Bounding Box:', box);

                    console.log('📦 Bounding Box Min:', box.min);
                    console.log('📦 Bounding Box Max:', box.max);
                    console.log('📦 Bounding Box Center:', box.getCenter(new THREE.Vector3()));
                    console.log('📦 Bounding Box Size:', box.getSize(new THREE.Vector3()));

                    // Füge das nach model.position.sub(center) hinzu:
                    console.log('🔍 Center vor sub():', center);
                    console.log('🔍 Model position vor sub():', model.position.clone());
                    model.position.sub(center);
                    console.log('🔍 Model position nach sub():', model.position);

                    // Teste ob das Modell wirklich zentriert ist:
                    const newBox = new THREE.Box3().setFromObject(model);
                    const newCenter = newBox.getCenter(new THREE.Vector3());
                    console.log('🎯 NEUES Zentrum nach sub():', newCenter);
                    console.log('🎯 Sollte ungefähr (0,0,0) sein!');
                    
                    // // DANN erstelle die Bounding Box Helper (nach der Zentrierung!)
                    // const boxHelper = new THREE.BoxHelper(model, 0xff0000); // Rote Linien
                    // scene.add(boxHelper);

                    // // Neue Box nach Zentrierung berechnen
                    // const boxAfterCentering = new THREE.Box3().setFromObject(model);
                    // const box3Helper = new THREE.Box3Helper(boxAfterCentering, 0x00ff00); // Grüne Linien
                    // scene.add(box3Helper);
                    
                    console.log('🎯 Modell Position nach Zentrierung:', model.position);
                    console.log('🎯 ModelGroup Position:', modelGroup.position);

                    // // Skaliere das Modell wenn es zu groß oder klein ist
                    // const maxDimension = Math.max(size.x, size.y, size.z);
                    // console.log('🔍 Max Dimension:', maxDimension);
                    // if (maxDimension > 1) {
                    //     const scale = 15 / maxDimension;
                    //     model.scale.setScalar(scale);
                    //     console.log('🔍 Modell skaliert auf:', scale);
                    // } else if (maxDimension < 0.1) {
                    //     const scale = 1 / maxDimension;
                    //     model.scale.setScalar(scale);
                    //     console.log('🔍 Modell vergrößert auf:', scale);
                    // }

                    // // for colossal_head_of_a_deva.glb
                    model.scale.setScalar(1);

                    // // for ganesha_wooden_mask.glb
                    // model.scale.setScalar(0.15);

                    // // for vans shoe
                    // model.scale.setScalar(30);
                    
                    // Entferne den Fallback-Cube
                    scene.remove(cube);
                    
                    // Füge das GLTF Modell hinzu
                    // scene.add(model);
                    modelGroup.add(model);
                    
                    // Schatten aktivieren und Material-Debug
                    let meshCount = 0;
                    model.traverse((child) => {
                        if (child.isMesh) {
                            meshCount++;
                            child.castShadow = true;
                            child.receiveShadow = true;
                            
                            // Debug: Material-Info
                            console.log(`🎨 Mesh ${meshCount}:`, child.name, 'Material:', child.material);
                            
                            // Falls Material-Problem: Fallback Material
                            if (!child.material || child.material.transparent === undefined) {
                                child.material = new THREE.MeshPhongMaterial({ 
                                    color: 0x8B4513,
                                    shininess: 30
                                });
                                console.log('🔧 Fallback Material angewendet für:', child.name);
                            }
                        }
                    });
                    
                    console.log(`🔢 Gefundene Meshes: ${meshCount}`);
                    
                    camera.lookAt(0, 0, 0);

                    // // for vans shoe and ganesha_wooden_mask
                    // camera.position.set(0, 0, 5);

                    camera.position.set(0, 0.1, 1.95);
                    
                    // console.log('📷 Kamera neu positioniert:', camera.position);

                    // Verstecke Loading Screen
                    loadingDiv.style.display = 'none';
                    
                    console.log('🎭 Nepalese Tribal Mask geladen und Animation gestartet!');
                },
                function (progress) {
                    console.log('📊 Lade-Fortschritt:', (progress.loaded / progress.total * 100) + '%');
                },
                function (error) {
                    console.error('❌ Fehler beim Laden des GLTF Modells:', error);
                    console.error('📝 Fehler Details:', error.message, error.stack);
                    loadingDiv.innerHTML = '<div style="color: #ff6b6b;">Fehler beim Laden des 3D Modells<br>Zeige Fallback-Cube<br><small>Prüfe Console für Details</small></div>';
                    setTimeout(() => {
                        loadingDiv.style.display = 'none';
                    }, 5000);
                }
            );

            const gui = new GUI();

            // Beispiel: Rotation & Position vom Modell steuern
            const settings = {
                modelGroupRotationY: 0,
                modelGroupPositionX: 0,
                modelGroupPositionY: 0,
                modelGroupPositionZ: 0,
                rotationY: 0,
                posX: 0,
                posY: 0,
                posZ: 0
            };

            gui.add(settings, 'modelGroupRotationY', -Math.PI, Math.PI).name('Model Group Rotation Y').onChange(v => {
                if (modelGroup) modelGroup.rotation.y = v;
            });
            gui.add(settings, 'modelGroupPositionX', -25, 25).name('Model Group Position X').onChange(v => {
                if (modelGroup) modelGroup.position.x = v;
            });
            gui.add(settings, 'modelGroupPositionY', -25, 25).name('Model Group Position Y').onChange(v => {
                if (modelGroup) modelGroup.position.y = v;
            });
            gui.add(settings, 'modelGroupPositionZ', -25, 25).name('Model Group Position Z').onChange(v => {
                if (modelGroup) modelGroup.position.z = v;
            });

            gui.add(settings, 'rotationY', -Math.PI, Math.PI).name('Rotation Y').onChange(v => {
                if (model) model.rotation.y = v;
            });

            gui.add(settings, 'posX', -25, 25).onChange(v => { 
                if (model) {
                    model.position.x = v;
                    console.log('Model Position nach X-Änderung:', model.position);
                }
            });

            gui.add(settings, 'posY', -25, 25).onChange(v => { 
                if (model) {
                    model.position.y = v;
                    console.log('Model Position nach Y-Änderung:', model.position);
                    console.log('Model in Scene?', scene.children.includes(modelGroup));
                }
            });

            gui.add(settings, 'posZ', -25, 25).onChange(v => { 
                if (model) {
                    model.position.z = v;
                    console.log('Model Position nach Z-Änderung:', model.position);
                    console.log('Kamera Position:', camera.position);
                }
            });

            // Beispiel: Lichtstärke
            gui.add(directionalLight, 'intensity', 0, 25).name('Lichtstärke');
            gui.add(directionalLight.position, 'x', -10, 10).name('Licht Position X');
            gui.add(directionalLight.position, 'y', -10, 10).name('Licht Position Y');
            gui.add(directionalLight.position, 'z', -10, 10).name('Licht Position Z');

            gui.add(fillLight, 'intensity', 0, 2.5).name('Fill-Lichtstärke');
            gui.add(ambientLight, 'intensity', 0, 100).name('Umgebungs-Lichtstärke');


            // constantly observe mouse position and update camera position
            let mouseX = 0;
            let mouseY = 0;

            window.addEventListener('mousemove', (event) => {
                mouseX = (event.clientX / window.innerWidth) * 2 - 1;
                mouseY = (event.clientY / window.innerHeight) * 2 - 1;
            });

            let baseRotationY = 0;
            let targetRotationY = 0;

            // Animation Loop
            function animate() {
                requestAnimationFrame(animate);


                
                if (model) {
                    // modelGroup.rotation.y += 0.005;
                    // set mouse position to rotate model
                    // modelGroup.rotation.y = mouseX * 2;
                    // modelGroup.rotation.z = mouseX * 0.25;
                    // modelGroup.rotation.x = mouseY * 0.25;


                    
                    // // rotate modelGroup suttle based on mouse position
                    // modelGroup.rotation.y = mouseX * 0.5;
                    // modelGroup.rotation.x = mouseY * 0.05;
                    // modelGroup.rotation.z = mouseX * 0.05;

                    // Ziel-Rotationen basierend auf Maus
                    const targetRotationY = mouseX * 0.8;
                    const targetRotationX = mouseY * 0.33;
                    const targetRotationZ = mouseX * 0.1;
                    
                    // Lerp zu den Ziel-Rotationen (0.1 = Lerp-Speed, 0.05 = langsamer, 0.2 = schneller)
                    modelGroup.rotation.y += (targetRotationY - modelGroup.rotation.y) * 0.05;
                    modelGroup.rotation.x += (targetRotationX - modelGroup.rotation.x) * 0.05;
                    modelGroup.rotation.z += (targetRotationZ - modelGroup.rotation.z) * 0.05;
                    
                    

                    /*
                    // Basis-Rotation läuft immer weiter
                    baseRotationY += 0.01;
                    
                    // Ziel-Rotation = Basis + Maus-Einfluss
                    targetRotationY = baseRotationY + (mouseX * 2);
                    
                    // // Smooth interpolation zur Ziel-Rotation (Lerp)
                    // modelGroup.rotation.y += (targetRotationY - modelGroup.rotation.y) * 0.1;
                    
                    // just ja stedy y rotation 
                    modelGroup.rotation.y += 0.005;
                    
                    modelGroup.rotation.x = mouseY * 0.25;
                    
                    // Zoom
                    const distance = Math.sqrt(Math.pow(mouseX, 2) + Math.pow(mouseY, 2));
                    const minZ = 2;
                    const maxZ = 2.5;
                    camera.position.z = maxZ - (distance * (maxZ - minZ));
                    */
                    

                    // cube.rotation.x += 0.01;
                    // cube.rotation.y += 0.01;
                } else {
                    // Rotiere den Fallback-Cube
                    cube.rotation.x += 0.01;
                    cube.rotation.y += 0.01;
                }
                
                renderer.render(scene, camera);
            }
            
            // Starte Animation
            animate();
            animationRunning = true;

            // Window Resize Handler
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });

            console.log('🚀 Three.js Scene mit Import Maps erfolgreich initialisiert!');
        });
    </script>



    <?php
    return ob_get_clean();
}

// Hex → RGBA Hilfsfunktion
function kishas_hex_to_rgba($color, $opacity = 1) {
    $color = str_replace('#', '', $color);
    if (strlen($color) == 6) {
        list($r, $g, $b) = array(
            hexdec(substr($color, 0, 2)),
            hexdec(substr($color, 2, 2)),
            hexdec(substr($color, 4, 2))
        );
    } elseif (strlen($color) == 3) {
        list($r, $g, $b) = array(
            hexdec(str_repeat(substr($color, 0, 1), 2)),
            hexdec(str_repeat(substr($color, 1, 1), 2)),
            hexdec(str_repeat(substr($color, 2, 1), 2))
        );
    } else {
        return 'rgba(0,0,0,' . floatval($opacity) . ')';
    }
    return 'rgba(' . $r . ',' . $g . ',' . $b . ',' . floatval($opacity) . ')';
}
?>