<?php

/**
 * custom vc element - custom icon with text 
 */
add_action('vc_before_init', 'vc_custom_element_nw_custom_icon_with_text');
function vc_custom_element_nw_custom_icon_with_text() {
    vc_map(array(
        'name' => 'Custom Icon with Text',
        'base' => 'nw_custom_icon_with_text',
        'category' => 'Inhalt',
        'description' => 'Ein Element mit Überschrift, Text und Bild als Icon aus der Mediathek.',
        'icon' => 'icon-wpbakery-custom-advisory',
        'category' => 'Kishas Custom Elements',
        'params' => array(
            array(
                'type' => 'attach_image',
                'heading' => 'Bild/Icon',
                'param_name' => 'icon_image',
                'value' => '',
                'description' => 'Wählen Sie ein Bild oder Icon aus der Mediathek',
                'admin_label' => true,
            ),
            array(
                'type' => 'textfield',
                'heading' => 'Icon-Größe (px)',
                'param_name' => 'icon_size',
                'value' => '50',
                'description' => 'Größe des Icons in Pixeln',
            ),
            array(
                'type' => 'textfield',
                'holder' => 'h3',
                'class' => '',
                'heading' => 'Überschrift',
                'param_name' => 'headline',
                'value' => '',
                'description' => 'Geben Sie die Überschrift ein',
                'admin_label' => true,
            ),
            array(
                'type' => 'textarea_html',
                'holder' => 'div',
                'class' => '',
                'heading' => 'Text',
                'param_name' => 'content',
                'value' => '',
            ),
            array(
                'type' => 'dropdown',
                'heading' => 'Ausrichtung',
                'param_name' => 'alignment',
                'value' => array(
                    'Links' => 'left',
                    'Zentriert' => 'center',
                ),
                'std' => 'left',
                'description' => 'Ausrichtung des Elements',
            ),
            array(
                'type' => 'colorpicker',
                'heading' => 'Überschriftfarbe',
                'param_name' => 'headline_color',
                'value' => '#333333',
                'description' => 'Farbe der Überschrift',
            ),
        )
    ));
}

add_shortcode('nw_custom_icon_with_text', 'nw_custom_icon_with_text_function');
function nw_custom_icon_with_text_function($atts, $content = null) {
    extract(shortcode_atts(array(
        'icon_image' => '',
        'icon_size' => '50',
        'headline' => '',
        'alignment' => 'left',
        'headline_color' => ''
    ), $atts));

    // Icon/Bild aus der Mediathek holen
    $icon_html = '';
    if (!empty($icon_image)) {
        $image_url = wp_get_attachment_image_url($icon_image, 'full');
        if ($image_url) {
            // Prüfen ob es sich um eine SVG-Datei handelt
            $attachment_id = intval($icon_image);
            $mime_type = get_post_mime_type($attachment_id);
            
            if ($mime_type === 'image/svg+xml') {
                // SVG als inline HTML ausgeben
                $svg_file = get_attached_file($attachment_id);
                if ($svg_file && file_exists($svg_file)) {
                    $svg_content = file_get_contents($svg_file);
                    // max-height direkt am SVG setzen
                    $svg_content = preg_replace('/<svg/', '<svg style="max-height: ' . esc_attr($icon_size) . 'px;"', $svg_content);
                    
                    $icon_html = sprintf(
                        '<div class="custom-icon-wrapper" style="justify-content:' . ($alignment === "center" ? "center" : "start") . '; max-height: ' . esc_attr($icon_size) . 'px;"";">%s</div>',
                        $svg_content
                    );
                }
            } else {
                // Normale Bilder als <img> Tag
                $icon_html = sprintf(
                    '<div class="custom-icon-wrapper"><img src="%s" alt="%s" style="width: %spx; height: auto;"></div>',
                    esc_url($image_url),
                    esc_attr($headline),
                    esc_attr($icon_size)
                );
            }
        }
    }

    // HTML für die Headline generieren
    $headline_html = '';
    if (!empty($headline)) {
        $headline_html = sprintf(
            '<h3 style="color: %s;">%s</h3>',
            esc_attr($headline_color),
            esc_html($headline)
        );
    }

    // Container-Klasse basierend auf Ausrichtung
    $container_class = 'nw-custom-icon-with-text';
    if ($alignment === 'center') {
        $container_class .= ' text-center';
    }

    // Kombiniertes HTML zurückgeben
    return "<div class='" . esc_attr($container_class) . "'>
                " . $icon_html . "
                <div class='content'>
                    " . $headline_html . "
                    <div>" . wpb_js_remove_wpautop($content, true) . "</div>
                </div>
            </div>";
}

