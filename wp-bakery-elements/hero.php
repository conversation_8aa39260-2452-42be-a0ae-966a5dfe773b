<?php

/**
 * Hero Section custom vc module 
 */
function vc_hero_module() {
    vc_map(array(
        'name' => 'Hero Section',
        'base' => 'hero_section',
        'description' => 'Ein Hero-Bereich mit Bild, Text und Logos',
        'category' => 'Content',
        'icon' => 'icon-wpb-images-stack',
        'category' => 'Kishas Custom Elements',
        'params' => array(
            // Main Image
            array(
                'type' => 'attach_image',
                'heading' => 'Hauptbild',
                'param_name' => 'main_image',
                'description' => 'Wählen Sie das Hauptbild für den Hero-Bereich',
                'admin_label' => true,
            ),
            // NEW: Google Reviews Option
            array(
                'type' => 'checkbox',
                'heading' => 'Google Reviews anzeigen?',
                'param_name' => 'show_google_reviews',
                'value' => array('Ja' => 'yes'),
                'description' => 'Google Reviews Badge unterhalb des Textes anzeigen',
            ),
            array(
                'type' => 'dropdown',
                'heading' => 'Review Anzeigetyp',
                'param_name' => 'reviews_display_type',
                'value' => array(
                    'Nur Gesamtübersicht' => 'summary',
                    'Alle Standorte' => 'all',
                    'Nur einzelne Standorte' => 'locations'
                ),
                'description' => 'Wähle, welche Review-Informationen angezeigt werden sollen',
                'dependency' => array(
                    'element' => 'show_google_reviews',
                    'value' => 'yes'
                )
            ),
            // Headline
            array(
                'type' => 'textarea',
                'heading' => 'Überschrift',
                'param_name' => 'headline',
                'description' => 'Die Hauptüberschrift des Hero-Bereichs',
                'admin_label' => true,
            ),
            // Paragraph
            array(
                'type' => 'textarea',
                'heading' => 'Text',
                'param_name' => 'paragraph',
                'description' => 'Der Beschreibungstext unter der Überschrift',
            ),
            // Multiple CTA Buttons
            array(
                'type' => 'param_group',
                'heading' => 'CTA Buttons',
                'param_name' => 'cta_buttons',
                'description' => 'Fügen Sie Call-to-Action Buttons hinzu',
                'params' => array(
                    array(
                        'type' => 'vc_link',
                        'heading' => 'Button Link',
                        'param_name' => 'button_link',
                        'description' => 'Link und Text für den Button',
                    ),
                    array(
                        'type' => 'checkbox',
                        'heading' => 'Primary Style',
                        'param_name' => 'is_primary',
                        'value' => array('Ja' => 'yes'),
                        'description' => 'Aktivieren für Primary Button Style (blau), deaktivieren für Secondary (transparent)',
                    ),
                ),
            ),
            // Logo Section Text
            array(
                'type' => 'textfield',
                'heading' => 'Logo-Bereich Text',
                'param_name' => 'logo_section_text',
                'description' => 'Text über den Logos (z.B. "Bei uns finden Sie eine vielfältige Auswahl an Klimaanlagen")',
            ),
            // Logo Images
            array(
                'type' => 'param_group',
                'heading' => 'Partner-Logos',
                'param_name' => 'partner_logos',
                'description' => 'Fügen Sie Partner-Logos hinzu',
                'params' => array(
                    array(
                        'type' => 'attach_image',
                        'heading' => 'Logo',
                        'param_name' => 'logo',
                        'description' => 'Wählen Sie ein Partner-Logo',
                    ),
                    array(
                        'type' => 'textfield',
                        'heading' => 'Firmenname',
                        'param_name' => 'company_name',
                        'description' => 'Name des Partners (wird als Alt-Text verwendet)',
                    ),
                )
            ),
        )
    ));
}
add_action('vc_before_init', 'vc_hero_module');

function hero_section_shortcode($atts, $content = null) {
    extract(shortcode_atts(array(
        'main_image' => '',
        'show_google_reviews' => '', 
        'reviews_display_type' => 'summary',
        'headline' => '',
        'paragraph' => '',
        'cta_buttons' => '',
        'logo_section_text' => '',
        'partner_logos' => ''
    ), $atts));

    // Get main image URL
    $main_image_url = wp_get_attachment_image_url($main_image, 'full');

    $show_reviews = ($show_google_reviews === 'yes'); 

    // Parse CTA buttons - Diese Zeile fehlte!
    $cta_buttons = vc_param_group_parse_atts($atts['cta_buttons']);
    
    // Parse partner logos
    $logos = vc_param_group_parse_atts($partner_logos);
    
    // Start building output
    ob_start();
    ?>
    <div class="hero-section">
        <div class="hero-content">
            <div class="hero-grid">
                <!-- Left image column -->
                <?php if (!empty($main_image_url)) : ?>
                    <div class="hero-image">
                        <img src="<?php echo esc_url($main_image_url); ?>" alt="<?php echo esc_attr($headline); ?>">
                    </div>
                <?php endif; ?>

                <!-- Right content column -->
                <div class="hero-text">
                <?php // Google Reviews Integration ?>
                    <?php if ($show_reviews) : ?>
                        <div class="hero-google-reviews">
                            <?php
                            // Get Google Reviews data directly from database
                            global $wpdb;
                            $table_name = $wpdb->prefix . 'newwways_google_review_badges';
                            $results = $wpdb->get_results("SELECT * FROM $table_name", OBJECT);
                            
                            if (count($results) > 0) {
                                $total_ratings = 0;
                                $total_reviews = 0;
                                $first_place_id = '';
                                
                                foreach ($results as $row) {
                                    $total_ratings += ($row->rating * $row->user_ratings_total);
                                    $total_reviews += $row->user_ratings_total;
                                    if (empty($first_place_id)) {
                                        $first_place_id = $row->place_id;
                                    }
                                }
                                
                                $average_rating = round($total_ratings / $total_reviews, 1);
                                $google_maps_url = 'https://www.google.com/maps/place/?q=place_id:' . $first_place_id;
                                
                                // Generate star rating
                                $full_stars = floor($average_rating);
                                $half_star = ($average_rating - $full_stars) >= 0.5;
                                $empty_stars = 5 - ceil($average_rating);
                                
                                $stars_html = '<div class="google-stars" style="display: inline-flex; color: #FDB300;">';
                                
                                // Full stars
                                for ($i = 0; $i < $full_stars; $i++) {
                                    $stars_html .= '<svg style="width: 18px; height: 18px; margin-right: 2px;" viewBox="0 0 24 24">
                                        <path fill="currentColor" d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
                                    </svg>';
                                }
                                
                                // Half star if needed
                                if ($half_star) {
                                    $stars_html .= '<svg style="width: 18px; height: 18px; margin-right: 2px;" viewBox="0 0 24 24">
                                        <path fill="currentColor" d="M12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21L12 17.27V2z"/>
                                    </svg>';
                                }
                                
                                // Empty stars
                                for ($i = 0; $i < $empty_stars; $i++) {
                                    $stars_html .= '<svg style="width: 18px; height: 18px; margin-right: 2px;" viewBox="0 0 24 24">
                                        <path fill="#e7e7e7" d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
                                    </svg>';
                                }
                                
                                $stars_html .= '</div>';
                                ?>
                                
                                <a href="<?php echo esc_url($google_maps_url); ?>" target="_blank" rel="noopener noreferrer" style="text-decoration: none; color: inherit;">
                                    <div class="google-reviews-section">
                                        <!-- Google Logo -->
                                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E" alt="Google" style="width: 20px; height: 20px;">
                                        
                                        <!-- Stars and Rating -->
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                                            <?php echo $stars_html; ?>
                                            <p style="margin: 0 0 0 0; font-size: 14px;">
                                                <strong><?php echo $average_rating; ?> Sterne</strong> basierend auf <strong><?php echo $total_reviews; ?></strong> Bewertungen
                                            </p>
                                        </div>
                                    </div>
                                </a>
                                
                            <?php } ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($headline)) : ?>
                        <h1><?php echo $headline; ?></h1>
                    <?php endif; ?>

                    <?php if (!empty($paragraph)) : ?>
                        <p><?php echo wpautop($paragraph); ?></p>
                    <?php endif; ?>
                    
                    <?php if (!empty($cta_buttons)) : ?>
                        <div class="cta-buttons">
                            <?php foreach ($cta_buttons as $button) :
                                $link = vc_build_link($button['button_link']);
                                if (!empty($link['url'])) :
                                    $button_class = 'cta-button';
                                    if (empty($button['is_primary']) || $button['is_primary'] !== 'yes') {
                                        $button_class .= ' secondary';
                                    }
                                    ?>
                                    <a href="<?php echo esc_url($link['url']); ?>" 
                                       class="<?php echo esc_attr($button_class); ?>"
                                       <?php echo !empty($link['target']) ? 'target="' . esc_attr($link['target']) . '"' : ''; ?>>
                                        <?php echo esc_html($link['title']); ?>
                                    </a>
                                <?php endif;
                            endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                
            </div>

            <!-- Logo section -->
            <?php if (!empty($logo_section_text) || !empty($logos)) : ?>
                <div class="partner-section">
                    <?php if (!empty($logo_section_text)) : ?>
                        <p class="partner-text"><?php echo esc_html($logo_section_text); ?></p>
                    <?php endif; ?>

                    <?php if (!empty($logos)) : ?>
                        <div class="partner-logos">
                            <?php foreach ($logos as $logo) : 
                                $logo_url = wp_get_attachment_image_url($logo['logo'], 'full');
                                if ($logo_url) : ?>
                                    <div class="partner-logo">
                                        <img src="<?php echo esc_url($logo_url); ?>" 
                                             alt="<?php echo esc_attr($logo['company_name']); ?>">
                                    </div>
                                <?php endif;
                            endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php
    return ob_get_clean();
}
add_shortcode('hero_section', 'hero_section_shortcode');
