<?php

/**
 * custom vc element - icon with text 
 */
add_action('vc_before_init', 'vc_custom_element_nw_icon_with_text');
function vc_custom_element_nw_icon_with_text() {
    vc_map(array(
        'name' => 'Icon with text',
        'base' => 'nw_icon_with_text',
        'description' => 'Ein Element mit Überschrift, Text und wählbarem Icon.',
        'icon' => 'icon-wpbakery-custom-advisory',
        'category' => 'VDI Custom Elements',
        'params' => array(
            // appearance
            array(
                'type' => 'dropdown',
                'heading' => 'Ausrichtung',
                'param_name' => 'alignment',
                'value' => array(
                    'Links' => 'left',
                    'Zentriert' => 'center',
                ),
                'std' => 'left',
                'description' => 'Ausrichtung des Elements',
            ),
            array(
                'type' => 'textfield',
                'holder' => 'h3',
                'class' => '',
                'heading' => 'Überschrift',
                'param_name' => 'headline',
                'value' => '',
                'description' => 'Geben Sie die Überschrift ein',
                'admin_label' => true,
            ),
            array(
                'type' => 'textarea_html',
                'holder' => 'div',
                'class' => '',
                'heading' => 'Text',
                'param_name' => 'content',
                'value' => '',
            ),
            // dropdown for h type (h1, h2)
            array(
                'type' => 'dropdown',
                'heading' => 'Überschrift Typ',
                'param_name' => 'headline_type',
                'value' => array(
                    'H3' => 'h3',
                    'H4' => 'h4',
                    'H5' => 'h5',
                ),
                'description' => 'Wählen Sie den Typ der Überschrift aus',
            ),
            array(
                'type' => 'dropdown',
                'heading' => 'Icon',
                'param_name' => 'icon_type',
                'value' => array(
                    'Standard Haken' => 'default_check',
                    'Option 2' => 'option2',
                    'Option 3' => 'option3'
                ),
                'description' => 'Wählen Sie ein Icon aus',
            )
        )
    ));
}


add_shortcode('nw_icon_with_text', 'nw_icon_with_text_function');
function nw_icon_with_text_function($atts, $content = null) {
    extract(shortcode_atts(array(
        'headline' => '',
        'headline_type' => 'h3',
        'alignment' => 'left',
        'icon_type' => 'default_check'
    ), $atts));

    $alignment_class = '';
    if ($alignment === 'center') {
        $container_class .= ' text-center';
    }

    $icon_html = '';
    switch ($icon_type) {
        case 'default_check':
            $icon_html = '<span class="vc_icon_element-icon fa fa-check"></span>'; // Standard-Haken
            break;
        case 'option2':
            $icon_html = '<span class="vc_icon_element-icon fa fa-adjust"></span>'; // Beispiel für ein anderes Icon
            break;
        case 'option3':
            $icon_html = '<span class="vc_icon_element-icon fa fa-anchor"></span>'; // Noch ein anderes Icon
            break;
    }

    // HTML für die Headline generieren
    $headline_html = '';
    switch ($headline_type) {
        case 'h3':
            $headline_html = "<h3>{$headline}</h3>";
            break;
        case 'h4':
            $headline_html = "<h4>{$headline}</h4>";
            break;
        case 'h5':
            $headline_html = "<h5>{$headline}</h5>";
            break;
    }

    // Kombiniertes HTML zurückgeben
    return "<div class='nw-icon-with-text {alignment_class}'>
                {$icon_html}
                <div class='content'>
                    {$headline_html}
                    <div>" . wpb_js_remove_wpautop($content, true) . "</div>
                </div>
            </div>";
}
